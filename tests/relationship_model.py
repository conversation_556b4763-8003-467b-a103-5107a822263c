#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Table
from sqlalchemy.orm import DeclarativeBase, Mapped, MappedAsDataclass, mapped_column, relationship


class Base(MappedAsDataclass, DeclarativeBase):
    pass


# Association table for User-Role many-to-many relationship
user_role = Table(
    'user_role',
    Base.metadata,
    mapped_column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    mapped_column('role_id', Integer, ForeignKey('role.id'), primary_key=True),
)


@MappedAsDataclass
class User(Base):
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    name: Mapped[str] = mapped_column(String(50))

    # Relationships
    profile: Mapped['Profile'] = relationship(back_populates='user', init=False)
    posts: Mapped[list['Post']] = relationship(back_populates='author', init=False)
    roles: Mapped[list['Role']] = relationship(secondary=user_role, back_populates='users', init=False)


@MappedAsDataclass
class Profile(Base):
    __tablename__ = 'profile'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    user_id: Mapped[int] = mapped_column(ForeignKey('user.id'))
    bio: Mapped[str] = mapped_column(String(200))

    # Relationship
    user: Mapped['User'] = relationship(back_populates='profile', init=False)


@MappedAsDataclass
class Post(Base):
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    title: Mapped[str] = mapped_column(String(100))
    author_id: Mapped[int] = mapped_column(ForeignKey('user.id'))

    # Relationship
    author: Mapped['User'] = relationship(back_populates='posts', init=False)


@MappedAsDataclass
class Role(Base):
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    name: Mapped[str] = mapped_column(String(50))

    # Relationship
    users: Mapped[list['User']] = relationship(secondary=user_role, back_populates='roles', init=False)
