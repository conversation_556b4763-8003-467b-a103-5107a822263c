#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的关系查询测试
测试 join_xxx 参数的各种用法
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload, subqueryload

from sqlalchemy_crud_plus import CRUDPlus
from tests.models import User, Post, Profile, Category, Comment, Tag, Role


class TestBasicRelationshipLoading:
    """测试基本关系加载功能"""

    @pytest.mark.asyncio
    async def test_join_one_to_one(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试一对一关系加载 - join_profile"""
        users = sample_data['users']
        
        # 使用 join_profile=True (默认策略)
        user = await user_crud.select_model(
            db_session, users[0].id, join_profile=True
        )
        
        assert user is not None
        assert user.profile is not None
        assert user.profile.user_id == users[0].id

    @pytest.mark.asyncio
    async def test_join_one_to_many(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试一对多关系加载 - join_posts"""
        users = sample_data['users']
        
        # 使用 join_posts='selectinload'
        user = await user_crud.select_model(
            db_session, users[0].id, join_posts='selectinload'
        )
        
        assert user is not None
        assert len(user.posts) > 0
        for post in user.posts:
            assert post.author_id == users[0].id

    @pytest.mark.asyncio
    async def test_join_many_to_many(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试多对多关系加载 - join_roles"""
        users = sample_data['users']
        
        # 使用 join_roles='selectinload'
        user = await user_crud.select_model(
            db_session, users[0].id, join_roles='selectinload'
        )
        
        assert user is not None
        assert len(user.roles) > 0

    @pytest.mark.asyncio
    async def test_join_self_referential(
        self, db_session: AsyncSession, sample_data: dict, category_crud: CRUDPlus[Category]
    ):
        """测试自引用关系加载 - join_children"""
        categories = sample_data['categories']
        # 找到根分类
        root_category = next(cat for cat in categories if cat.parent_id is None)
        
        category = await category_crud.select_model(
            db_session, root_category.id, join_children=True
        )
        
        assert category is not None
        assert len(category.children) > 0

    @pytest.mark.asyncio
    async def test_multiple_joins(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试多个关系同时加载"""
        users = sample_data['users']
        
        user = await user_crud.select_model(
            db_session, users[0].id,
            join_posts='selectinload',
            join_profile='joinedload',
            join_roles=True,
            join_comments='selectinload'
        )
        
        assert user is not None
        assert len(user.posts) > 0
        assert user.profile is not None
        assert len(user.roles) > 0


class TestLoadingStrategies:
    """测试不同的加载策略"""

    @pytest.mark.asyncio
    async def test_selectinload_strategy(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试 selectinload 策略"""
        users = await user_crud.select_models(
            db_session, join_posts='selectinload'
        )
        
        assert len(users) > 0
        for user in users:
            # 验证关系已加载
            assert hasattr(user, 'posts')

    @pytest.mark.asyncio
    async def test_joinedload_strategy(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试 joinedload 策略"""
        users = await user_crud.select_models(
            db_session, join_profile='joinedload'
        )
        
        assert len(users) > 0

    @pytest.mark.asyncio
    async def test_subqueryload_strategy(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试 subqueryload 策略"""
        users = await user_crud.select_models(
            db_session, join_posts='subqueryload'
        )
        
        assert len(users) > 0

    @pytest.mark.asyncio
    async def test_mixed_strategies(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试混合策略"""
        users = await user_crud.select_models(
            db_session,
            join_posts='selectinload',
            join_profile='joinedload',
            join_roles='subqueryload'
        )
        
        assert len(users) > 0


class TestWithRelationships:
    """测试 with_xxx 简化语法"""

    @pytest.mark.asyncio
    async def test_with_posts(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试 with_posts=True 简化语法"""
        users = sample_data['users']
        
        user = await user_crud.select_model(
            db_session, users[0].id, with_posts=True
        )
        
        assert user is not None
        assert len(user.posts) > 0

    @pytest.mark.asyncio
    async def test_with_multiple_relationships(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试多个 with_xxx 关系"""
        users = sample_data['users']
        
        user = await user_crud.select_model(
            db_session, users[0].id,
            with_posts=True,
            with_profile=True,
            with_roles=True
        )
        
        assert user is not None
        assert len(user.posts) > 0
        assert user.profile is not None
        assert len(user.roles) > 0


class TestComplexQueries:
    """测试复杂查询场景"""

    @pytest.mark.asyncio
    async def test_select_models_with_relationships(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试批量查询与关系加载"""
        users = await user_crud.select_models(
            db_session,
            join_posts='selectinload',
            join_profile='joinedload'
        )
        
        assert len(users) > 0
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')

    @pytest.mark.asyncio
    async def test_select_models_order_with_relationships(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试排序查询与关系加载"""
        users = await user_crud.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            join_posts=True
        )
        
        assert len(users) > 0
        # 验证排序
        names = [user.name for user in users]
        assert names == sorted(names)

    @pytest.mark.asyncio
    async def test_pagination_with_relationships(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试分页查询与关系加载"""
        page1 = await user_crud.select_models(
            db_session,
            join_posts=True,
            limit=2,
            offset=0
        )
        
        page2 = await user_crud.select_models(
            db_session,
            join_posts=True,
            limit=2,
            offset=2
        )
        
        assert len(page1) <= 2
        assert len(page2) <= 2
        
        # 验证无重叠
        page1_ids = {user.id for user in page1}
        page2_ids = {user.id for user in page2}
        assert page1_ids.isdisjoint(page2_ids)

    @pytest.mark.asyncio
    async def test_filter_with_relationships(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试过滤条件与关系加载结合"""
        users = await user_crud.select_models(
            db_session,
            is_active=True,
            join_posts='selectinload',
            join_profile='joinedload'
        )
        
        assert len(users) > 0
        for user in users:
            assert user.is_active is True
            assert hasattr(user, 'posts')


class TestNestedRelationships:
    """测试嵌套关系（如果支持）"""

    @pytest.mark.asyncio
    async def test_nested_relationship_posts_author(
        self, db_session: AsyncSession, sample_data: dict, post_crud: CRUDPlus[Post]
    ):
        """测试嵌套关系 - posts.author"""
        posts = await post_crud.select_models(
            db_session,
            join_author='selectinload'
        )
        
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None
            assert post.author.name is not None


class TestPerformance:
    """测试性能相关功能"""

    @pytest.mark.asyncio
    async def test_n_plus_one_prevention(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试 N+1 查询预防"""
        users = await user_crud.select_models(
            db_session, join_posts='selectinload'
        )
        
        assert len(users) > 0
        
        # 访问关系数据不应触发额外查询
        for user in users:
            posts_count = len(user.posts)
            assert posts_count >= 0

    @pytest.mark.asyncio
    async def test_bulk_relationship_loading(
        self, db_session: AsyncSession, sample_data: dict, user_crud: CRUDPlus[User]
    ):
        """测试批量关系加载"""
        users = await user_crud.select_models(
            db_session,
            join_posts='selectinload',
            join_profile='joinedload',
            join_roles='selectinload',
            join_comments='selectinload'
        )
        
        assert len(users) > 0
        
        # 验证所有关系都已加载
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')
            assert hasattr(user, 'comments')
