#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple relationship tests with clean models and direct options usage.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_model import User, Post, Profile, Role
from tests.relationship_schema import UserCreate, PostCreate, ProfileCreate, RoleCreate


class TestRelationshipLoading:
    """Test relationship loading using options parameter."""

    @pytest.mark.asyncio
    async def test_one_to_one_loading(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test loading one-to-one relationship."""
        users = sample_data['users']
        user = await user_crud.select_model(
            relationship_session, users[0].id, options=[selectinload(User.profile)]
        )
        
        assert user is not None
        assert user.profile is not None

    @pytest.mark.asyncio
    async def test_one_to_many_loading(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test loading one-to-many relationship."""
        users = sample_data['users']
        user = await user_crud.select_model(
            relationship_session, users[0].id, options=[selectinload(User.posts)]
        )
        
        assert user is not None
        assert len(user.posts) > 0

    @pytest.mark.asyncio
    async def test_many_to_many_loading(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test loading many-to-many relationship."""
        users = sample_data['users']
        user = await user_crud.select_model(
            relationship_session, users[0].id, options=[selectinload(User.roles)]
        )
        
        assert user is not None
        assert len(user.roles) > 0

    @pytest.mark.asyncio
    async def test_multiple_relationships(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test loading multiple relationships."""
        users = sample_data['users']
        user = await user_crud.select_model(
            relationship_session, users[0].id, 
            options=[selectinload(User.posts), selectinload(User.profile), selectinload(User.roles)]
        )
        
        assert user is not None
        assert len(user.posts) > 0
        assert len(user.roles) > 0


class TestLoadingStrategies:
    """Test different loading strategies."""

    @pytest.mark.asyncio
    async def test_selectinload_strategy(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test selectinload strategy."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)]
        )
        
        assert len(users) > 0

    @pytest.mark.asyncio
    async def test_joinedload_strategy(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test joinedload strategy."""
        users = await user_crud.select_models(
            relationship_session,
            options=[joinedload(User.profile)]
        )
        
        assert len(users) > 0


class TestBasicQueries:
    """Test basic queries with relationship loading."""

    @pytest.mark.asyncio
    async def test_select_models_with_options(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test select_models with options."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts), selectinload(User.profile)]
        )
        
        assert len(users) > 0
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')

    @pytest.mark.asyncio
    async def test_select_models_order_with_options(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test select_models_order with options."""
        users = await user_crud.select_models_order(
            relationship_session,
            sort_columns='name',
            sort_orders='asc',
            options=[selectinload(User.posts)]
        )
        
        assert len(users) > 0
        # Verify sorting
        names = [user.name for user in users]
        assert names == sorted(names)

    @pytest.mark.asyncio
    async def test_pagination_with_options(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test pagination with relationship loading."""
        page1 = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            limit=2,
            offset=0
        )
        
        page2 = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            limit=2,
            offset=2
        )
        
        assert len(page1) <= 2
        assert len(page2) <= 2
        
        # Verify no overlap
        page1_ids = {user.id for user in page1}
        page2_ids = {user.id for user in page2}
        assert page1_ids.isdisjoint(page2_ids)


class TestPerformance:
    """Test performance aspects."""

    @pytest.mark.asyncio
    async def test_n_plus_one_prevention(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test that relationship loading prevents N+1 queries."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)]
        )
        
        assert len(users) > 0
        
        # Access posts for each user - should not trigger additional queries
        for user in users:
            posts_count = len(user.posts)
            assert posts_count >= 0

    @pytest.mark.asyncio
    async def test_bulk_loading(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test efficient bulk loading."""
        users = await user_crud.select_models(
            relationship_session,
            options=[
                selectinload(User.posts),
                joinedload(User.profile),
                selectinload(User.roles)
            ]
        )
        
        assert len(users) > 0
        
        # Verify all relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')
