#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型定义 - 专注于关系测试，最小化冗余字段
"""
from __future__ import annotations

from sqlalchemy import Column, Foreign<PERSON>ey, Integer, String, Table
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship


class Base(DeclarativeBase):
    """测试模型基类"""
    pass


# 多对多关联表
user_role = Table(
    'user_role',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    Column('role_id', Integer, Foreign<PERSON>ey('role.id'), primary_key=True),
)

post_tag = Table(
    'post_tag', 
    Base.metadata,
    Column('post_id', Integer, ForeignKey('post.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tag.id'), primary_key=True),
)


class User(Base):
    """用户模型 - 演示所有关系类型"""
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))

    # 一对一关系
    profile: Mapped[Profile | None] = relationship(back_populates='user', uselist=False)
    
    # 一对多关系
    posts: Mapped[list[Post]] = relationship(back_populates='author')
    
    # 多对多关系
    roles: Mapped[list[Role]] = relationship(secondary=user_role, back_populates='users')


class Profile(Base):
    """用户档案 - 一对一关系"""
    __tablename__ = 'profile'

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey('user.id'), unique=True)
    bio: Mapped[str] = mapped_column(String(200))

    # 反向关系
    user: Mapped[User] = relationship(back_populates='profile')


class Post(Base):
    """文章模型 - 多种关系"""
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(String(100))
    author_id: Mapped[int] = mapped_column(ForeignKey('user.id'))
    category_id: Mapped[int | None] = mapped_column(ForeignKey('category.id'))

    # 多对一关系
    author: Mapped[User] = relationship(back_populates='posts')
    category: Mapped[Category | None] = relationship(back_populates='posts')
    
    # 多对多关系
    tags: Mapped[list[Tag]] = relationship(secondary=post_tag, back_populates='posts')


class Category(Base):
    """分类 - 自引用关系"""
    __tablename__ = 'category'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    parent_id: Mapped[int | None] = mapped_column(ForeignKey('category.id'))

    # 自引用关系
    parent: Mapped[Category | None] = relationship('Category', remote_side=[id], back_populates='children')
    children: Mapped[list[Category]] = relationship('Category', back_populates='parent')
    
    # 一对多关系
    posts: Mapped[list[Post]] = relationship(back_populates='category')


class Tag(Base):
    """标签 - 多对多关系"""
    __tablename__ = 'tag'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50), unique=True)

    # 多对多关系
    posts: Mapped[list[Post]] = relationship(secondary=post_tag, back_populates='tags')


class Role(Base):
    """角色 - 多对多关系"""
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50), unique=True)

    # 多对多关系
    users: Mapped[list[User]] = relationship(secondary=user_role, back_populates='roles')
