#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的测试模型和 Schema 定义
包含所有类型的关系：一对一、一对多、多对多、自引用
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict
from sqlalchemy import Column, ForeignKey, Integer, String, Table, Text, Boolean, DateTime, func
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship


class Base(DeclarativeBase):
    """测试模型基类"""
    pass


# 多对多关联表
user_role = Table(
    'user_role',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('role.id'), primary_key=True),
)

post_tag = Table(
    'post_tag',
    Base.metadata,
    Column('post_id', Integer, ForeignKey('post.id'), primary_key=True),
    Column('tag_id', Integer, Foreign<PERSON><PERSON>('tag.id'), primary_key=True),
)


# ==================== 模型定义 ====================

class User(Base):
    """用户模型 - 演示一对一、一对多、多对多关系"""
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    email: Mapped[str] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())

    # 一对一关系
    profile: Mapped[Optional['Profile']] = relationship(back_populates='user', uselist=False)

    # 一对多关系
    posts: Mapped[list['Post']] = relationship(back_populates='author', cascade='all, delete-orphan')
    comments: Mapped[list['Comment']] = relationship(back_populates='author')

    # 多对多关系
    roles: Mapped[list['Role']] = relationship(secondary=user_role, back_populates='users')


class Profile(Base):
    """用户档案 - 一对一关系"""
    __tablename__ = 'profile'

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey('user.id'), unique=True)
    bio: Mapped[Optional[str]] = mapped_column(Text)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(200))

    # 反向关系
    user: Mapped['User'] = relationship(back_populates='profile')


class Category(Base):
    """分类 - 自引用关系"""
    __tablename__ = 'category'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    parent_id: Mapped[Optional[int]] = mapped_column(ForeignKey('category.id'))

    # 自引用关系
    parent: Mapped[Optional['Category']] = relationship('Category', remote_side=[id], back_populates='children')
    children: Mapped[list['Category']] = relationship('Category', back_populates='parent')

    # 一对多关系
    posts: Mapped[list['Post']] = relationship(back_populates='category')


class Post(Base):
    """文章 - 多种关系类型"""
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(String(100))
    content: Mapped[str] = mapped_column(Text)
    author_id: Mapped[int] = mapped_column(ForeignKey('user.id'))
    category_id: Mapped[Optional[int]] = mapped_column(ForeignKey('category.id'))
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    view_count: Mapped[int] = mapped_column(Integer, default=0)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())

    # 多对一关系
    author: Mapped['User'] = relationship(back_populates='posts')
    category: Mapped[Optional['Category']] = relationship(back_populates='posts')

    # 一对多关系
    comments: Mapped[list['Comment']] = relationship(back_populates='post', cascade='all, delete-orphan')

    # 多对多关系
    tags: Mapped[list['Tag']] = relationship(secondary=post_tag, back_populates='posts')


class Comment(Base):
    """评论 - 嵌套关系"""
    __tablename__ = 'comment'

    id: Mapped[int] = mapped_column(primary_key=True)
    content: Mapped[str] = mapped_column(Text)
    author_id: Mapped[int] = mapped_column(ForeignKey('user.id'))
    post_id: Mapped[int] = mapped_column(ForeignKey('post.id'))
    parent_id: Mapped[Optional[int]] = mapped_column(ForeignKey('comment.id'))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())

    # 关系定义
    author: Mapped['User'] = relationship(back_populates='comments')
    post: Mapped['Post'] = relationship(back_populates='comments')

    # 自引用关系（回复评论）
    parent: Mapped[Optional['Comment']] = relationship('Comment', remote_side=[id], back_populates='replies')
    replies: Mapped[list['Comment']] = relationship('Comment', back_populates='parent')


class Tag(Base):
    """标签 - 多对多关系"""
    __tablename__ = 'tag'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50), unique=True)
    color: Mapped[Optional[str]] = mapped_column(String(7))  # 颜色代码

    # 多对多关系
    posts: Mapped[list['Post']] = relationship(secondary=post_tag, back_populates='tags')


class Role(Base):
    """角色 - 多对多关系"""
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50), unique=True)
    description: Mapped[Optional[str]] = mapped_column(String(200))

    # 多对多关系
    users: Mapped[list['User']] = relationship(secondary=user_role, back_populates='roles')


# ==================== Pydantic Schema 定义 ====================

class UserCreate(BaseModel):
    name: str
    email: str
    is_active: bool = True


class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    email: str
    is_active: bool
    created_at: datetime


class ProfileCreate(BaseModel):
    bio: Optional[str] = None
    avatar_url: Optional[str] = None


class ProfileResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    user_id: int
    bio: Optional[str]
    avatar_url: Optional[str]


class PostCreate(BaseModel):
    title: str
    content: str
    category_id: Optional[int] = None
    is_published: bool = False


class PostResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    content: str
    author_id: int
    category_id: Optional[int]
    is_published: bool
    view_count: int
    created_at: datetime


class CategoryCreate(BaseModel):
    name: str
    parent_id: Optional[int] = None


class CategoryResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    parent_id: Optional[int]


class CommentCreate(BaseModel):
    content: str
    post_id: int
    parent_id: Optional[int] = None


class CommentResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    content: str
    author_id: int
    post_id: int
    parent_id: Optional[int]
    created_at: datetime


class TagCreate(BaseModel):
    name: str
    color: Optional[str] = None


class TagResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    color: Optional[str]


class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None


class RoleResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    description: Optional[str]
