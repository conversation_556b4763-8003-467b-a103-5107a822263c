#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Schema 定义 - 使用 Python 3.10+ 类型提示
"""
from __future__ import annotations

from pydantic import BaseModel, ConfigDict


# ==================== Create Schemas ====================

class UserCreate(BaseModel):
    name: str


class ProfileCreate(BaseModel):
    bio: str


class PostCreate(BaseModel):
    title: str
    category_id: int | None = None


class CategoryCreate(BaseModel):
    name: str
    parent_id: int | None = None


class TagCreate(BaseModel):
    name: str


class RoleCreate(BaseModel):
    name: str


# ==================== Update Schemas ====================

class UserUpdate(BaseModel):
    name: str | None = None


class ProfileUpdate(BaseModel):
    bio: str | None = None


class PostUpdate(BaseModel):
    title: str | None = None
    category_id: int | None = None


class CategoryUpdate(BaseModel):
    name: str | None = None
    parent_id: int | None = None


class TagUpdate(BaseModel):
    name: str | None = None


class RoleUpdate(BaseModel):
    name: str | None = None


# ==================== Response Schemas ====================

class UserResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str


class ProfileResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    bio: str


class PostResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    title: str
    author_id: int
    category_id: int | None


class CategoryResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    parent_id: int | None


class TagResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str


class RoleResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
