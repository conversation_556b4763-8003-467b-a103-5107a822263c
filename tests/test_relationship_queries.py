#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive tests for SQLAlchemy 2.0 relationship queries.
Tests all types of relationships and query operations.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload, subqueryload

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_model import User, Post, Category, Role, UserProfile
from tests.relationship_schema import (
    UserCreate, PostCreate, CategoryCreate, RoleCreate, UserProfileCreate
)


class TestBasicRelationshipQueries:
    """Test basic relationship loading and querying."""

    @pytest.mark.asyncio
    async def test_one_to_one_relationship(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test one-to-one relationship loading."""
        from sqlalchemy.orm import selectinload

        users = sample_data['users']
        user_with_profile = await user_crud.select_model(
            relationship_session, users[0].id, options=[selectinload(User.profile)]
        )

        assert user_with_profile is not None
        assert user_with_profile.profile is not None
        assert user_with_profile.profile.user_id == users[0].id

    @pytest.mark.asyncio
    async def test_one_to_many_relationship(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test one-to-many relationship loading."""
        users = sample_data['users']
        user_with_posts = await user_crud.select_model_with_relationships(
            relationship_session, users[0].id, relationships='posts'
        )
        
        assert user_with_posts is not None
        assert len(user_with_posts.posts) > 0
        for post in user_with_posts.posts:
            assert post.author_id == users[0].id

    @pytest.mark.asyncio
    async def test_many_to_many_relationship(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test many-to-many relationship loading."""
        users = sample_data['users']
        user_with_roles = await user_crud.select_model_with_relationships(
            relationship_session, users[0].id, relationships='roles'
        )
        
        assert user_with_roles is not None
        assert len(user_with_roles.roles) > 0

    @pytest.mark.asyncio
    async def test_self_referential_relationship(
        self, sample_data: dict, relationship_session: AsyncSession, category_crud: CRUDPlus[Category]
    ):
        """Test self-referential relationship loading."""
        categories = sample_data['categories']
        # Find a category with children
        parent_category = None
        for cat in categories:
            if cat.parent_id is None:
                parent_category = cat
                break
        
        assert parent_category is not None
        
        category_with_children = await category_crud.select_model_with_relationships(
            relationship_session, parent_category.id, relationships='children'
        )
        
        assert category_with_children is not None

    @pytest.mark.asyncio
    async def test_multiple_relationships(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test loading multiple relationships at once."""
        users = sample_data['users']
        user_with_all = await user_crud.select_model_with_relationships(
            relationship_session, users[0].id, relationships=['posts', 'profile', 'roles']
        )
        
        assert user_with_all is not None
        assert len(user_with_all.posts) > 0
        assert len(user_with_all.roles) > 0


class TestRelationshipFiltering:
    """Test filtering across relationships."""

    @pytest.mark.asyncio
    async def test_filter_by_relationship_field(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test filtering posts by author username."""
        users = sample_data['users']
        target_username = users[0].username
        
        posts = await post_crud.select_models(
            relationship_session,
            author__username=target_username
        )
        
        assert len(posts) > 0
        # Verify all posts belong to the target user
        for post in posts:
            assert post.author_id == users[0].id

    @pytest.mark.asyncio
    async def test_filter_by_relationship_with_operator(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test filtering with operators across relationships."""
        posts = await post_crud.select_models(
            relationship_session,
            author__username__like='user_%'
        )
        
        assert len(posts) > 0

    @pytest.mark.asyncio
    async def test_filter_by_multiple_relationship_fields(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test filtering by multiple relationship fields."""
        users = sample_data['users']
        categories = sample_data['categories']
        
        posts = await post_crud.select_models(
            relationship_session,
            author__username=users[0].username,
            category__name=categories[0].name
        )
        
        # Should return posts that match both conditions
        assert len(posts) >= 0

    @pytest.mark.asyncio
    async def test_count_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test counting with relationship filters."""
        users = sample_data['users']
        
        count = await post_crud.count_with_relationships(
            relationship_session,
            author__username=users[0].username
        )
        
        assert count >= 0

    @pytest.mark.asyncio
    async def test_exists_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test exists check with relationship filters."""
        users = sample_data['users']
        
        exists = await post_crud.exists(
            relationship_session,
            author__username=users[0].username
        )
        
        assert isinstance(exists, bool)


class TestLoadingStrategies:
    """Test different relationship loading strategies."""

    @pytest.mark.asyncio
    async def test_selectinload_strategy(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test selectinload strategy."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='posts',
            strategy='selectinload'
        )
        
        assert len(users) > 0
        for user in users:
            # Posts should be loaded
            assert hasattr(user, 'posts')

    @pytest.mark.asyncio
    async def test_joinedload_strategy(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test joinedload strategy."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='profile',
            strategy='joinedload'
        )
        
        assert len(users) > 0

    @pytest.mark.asyncio
    async def test_subqueryload_strategy(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test subqueryload strategy."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='posts',
            strategy='subqueryload'
        )
        
        assert len(users) > 0

    @pytest.mark.asyncio
    async def test_mixed_strategies(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test using different strategies for different relationships."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships={
                'posts': 'selectinload',
                'profile': 'joinedload',
                'roles': 'selectinload'
            }
        )
        
        assert len(users) > 0


class TestSortingWithRelationships:
    """Test sorting combined with relationship loading."""

    @pytest.mark.asyncio
    async def test_sort_with_relationships(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test sorting users while loading relationships."""
        users = await user_crud.select_models_order_with_relationships(
            relationship_session,
            sort_columns='username',
            sort_orders='asc',
            relationships=['posts', 'profile']
        )
        
        assert len(users) > 0
        # Verify sorting
        usernames = [user.username for user in users]
        assert usernames == sorted(usernames)

    @pytest.mark.asyncio
    async def test_sort_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test sorting with relationship filters."""
        posts = await post_crud.select_models_order_with_relationships(
            relationship_session,
            sort_columns='title',
            sort_orders='desc',
            relationships='author',
            author__username__like='user_%'
        )
        
        assert len(posts) >= 0
        if len(posts) > 1:
            titles = [post.title for post in posts]
            assert titles == sorted(titles, reverse=True)


class TestPaginationWithRelationships:
    """Test pagination combined with relationship loading."""

    @pytest.mark.asyncio
    async def test_pagination_with_relationships(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test pagination while loading relationships."""
        # Get first page
        page1 = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='posts',
            limit=2,
            offset=0
        )
        
        # Get second page
        page2 = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='posts',
            limit=2,
            offset=2
        )
        
        # Verify pagination
        assert len(page1) <= 2
        assert len(page2) <= 2
        
        # Verify no overlap
        page1_ids = {user.id for user in page1}
        page2_ids = {user.id for user in page2}
        assert page1_ids.isdisjoint(page2_ids)

    @pytest.mark.asyncio
    async def test_pagination_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test pagination with relationship filters."""
        posts = await post_crud.select_models_with_relationships(
            relationship_session,
            relationships='author',
            author__username__like='user_%',
            limit=3,
            offset=0
        )
        
        assert len(posts) <= 3


class TestComplexRelationshipQueries:
    """Test complex relationship query scenarios."""

    @pytest.mark.asyncio
    async def test_nested_relationship_access(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test accessing nested relationships."""
        posts = await post_crud.select_models_with_relationships(
            relationship_session,
            relationships=['author', 'category']
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author is not None
            assert post.author.username is not None

    @pytest.mark.asyncio
    async def test_relationship_with_regular_filters(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test combining relationship filters with regular filters."""
        posts = await post_crud.select_models(
            relationship_session,
            is_published=True,
            author__username__like='user_%'
        )

        assert len(posts) >= 0
        for post in posts:
            assert post.is_published is True

    @pytest.mark.asyncio
    async def test_update_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test updating records using relationship filters."""
        users = sample_data['users']

        async with relationship_session.begin():
            updated_count = await post_crud.update_model_by_column(
                relationship_session,
                {'view_count': 999},
                allow_multiple=True,
                author__username=users[0].username
            )

        assert updated_count >= 0

    @pytest.mark.asyncio
    async def test_delete_with_relationship_filter(
        self, sample_data: dict, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test deleting records using relationship filters."""
        # Create a test post first
        async with relationship_session.begin():
            test_post = Post(
                title='Test Delete Post',
                content='Content to delete',
                author_id=sample_data['users'][0].id,
                is_published=False
            )
            relationship_session.add(test_post)
            await relationship_session.flush()

            # Delete using relationship filter
            deleted_count = await post_crud.delete_model_by_column(
                relationship_session,
                allow_multiple=True,
                title='Test Delete Post',
                author__username=sample_data['users'][0].username
            )

        assert deleted_count >= 0


class TestErrorHandling:
    """Test error handling in relationship queries."""

    @pytest.mark.asyncio
    async def test_invalid_relationship_name(
        self, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test error handling for invalid relationship names."""
        with pytest.raises(Exception):  # Should raise RelationshipError
            await user_crud.select_model_with_relationships(
                relationship_session, 1, relationships='invalid_relationship'
            )

    @pytest.mark.asyncio
    async def test_invalid_loading_strategy(
        self, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test error handling for invalid loading strategies."""
        with pytest.raises(Exception):  # Should raise RelationshipError
            await user_crud.select_model_with_relationships(
                relationship_session, 1, relationships='posts', strategy='invalid_strategy'
            )

    @pytest.mark.asyncio
    async def test_invalid_relationship_filter(
        self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]
    ):
        """Test error handling for invalid relationship filters."""
        with pytest.raises(Exception):  # Should raise RelationshipError
            await post_crud.select_models(
                relationship_session,
                invalid_relationship__field='value'
            )


class TestPerformanceAndOptimization:
    """Test performance aspects of relationship queries."""

    @pytest.mark.asyncio
    async def test_n_plus_one_prevention(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test that relationship loading prevents N+1 queries."""
        # Load users with posts using selectinload
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships='posts',
            strategy='selectinload'
        )

        assert len(users) > 0

        # Access posts for each user - should not trigger additional queries
        for user in users:
            posts_count = len(user.posts)
            assert posts_count >= 0

    @pytest.mark.asyncio
    async def test_bulk_relationship_loading(
        self, sample_data: dict, relationship_session: AsyncSession, user_crud: CRUDPlus[User]
    ):
        """Test efficient bulk loading of relationships."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships=['posts', 'profile', 'roles'],
            strategy='selectinload'
        )

        assert len(users) > 0

        # Verify all relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')


class TestDataIntegrity:
    """Test data integrity in relationship operations."""

    @pytest.mark.asyncio
    async def test_relationship_consistency(
        self, sample_data: dict, relationship_session: AsyncSession,
        user_crud: CRUDPlus[User], post_crud: CRUDPlus[Post]
    ):
        """Test that relationship data is consistent."""
        users = sample_data['users']

        # Get user with posts
        user_with_posts = await user_crud.select_model_with_relationships(
            relationship_session, users[0].id, relationships='posts'
        )

        assert user_with_posts is not None

        # Verify each post's author_id matches the user's id
        for post in user_with_posts.posts:
            assert post.author_id == user_with_posts.id

            # Also verify by querying the post directly
            post_with_author = await post_crud.select_model_with_relationships(
                relationship_session, post.id, relationships='author'
            )
            assert post_with_author.author.id == user_with_posts.id

    @pytest.mark.asyncio
    async def test_many_to_many_consistency(
        self, sample_data: dict, relationship_session: AsyncSession,
        user_crud: CRUDPlus[User], role_crud: CRUDPlus[Role]
    ):
        """Test many-to-many relationship consistency."""
        users = sample_data['users']

        # Get user with roles
        user_with_roles = await user_crud.select_model_with_relationships(
            relationship_session, users[0].id, relationships='roles'
        )

        if user_with_roles and user_with_roles.roles:
            # Verify the reverse relationship
            for role in user_with_roles.roles:
                role_with_users = await role_crud.select_model_with_relationships(
                    relationship_session, role.id, relationships='users'
                )

                user_ids = [u.id for u in role_with_users.users]
                assert users[0].id in user_ids
