#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Async<PERSON>enerator

import pytest
import pytest_asyncio

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Base as BasicBase, Ins, InsPks
from tests.models import (
    Base, User, Post, Profile, Category, Comment, Tag, Role,
    UserCreate, PostCreate, ProfileCreate, CategoryCreate,
    CommentCreate, TagCreate, RoleCreate, user_role, post_tag
)

# Database configuration
_async_engine = create_async_engine(
    'sqlite+aiosqlite:///:memory:',
    future=True,
    echo=False,  # Set to True for SQL debugging
)
_async_session_factory = async_sessionmaker(_async_engine, autoflush=False, expire_on_commit=False)


@pytest_asyncio.fixture(scope='function', autouse=True)
async def setup_database() -> AsyncGenerator[None, None]:
    """Setup and teardown database for each test function."""
    async with _async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with _async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Provide a database session for testing."""
    async with _async_session_factory() as session:
        yield session


@pytest_asyncio.fixture
async def db_session_factory() -> AsyncGenerator[async_sessionmaker[AsyncSession], None]:
    """Provide a session factory for testing."""
    yield _async_session_factory


@pytest.fixture
def crud_ins() -> CRUDPlus[Ins]:
    """Provide CRUD instance for Ins model."""
    return CRUDPlus(Ins)


@pytest.fixture
def crud_ins_pks() -> CRUDPlus[InsPks]:
    """Provide CRUD instance for InsPks model."""
    return CRUDPlus(InsPks)


@pytest_asyncio.fixture
async def populated_db(db_session: AsyncSession, crud_ins: CRUDPlus[Ins]) -> list[Ins]:
    """Provide a database populated with test data."""
    async with db_session.begin():
        test_data = [Ins(name=f'item_{i}', del_flag=(i % 2 == 0)) for i in range(1, 11)]
        db_session.add_all(test_data)
        await db_session.flush()
        return test_data


@pytest_asyncio.fixture
async def populated_db_pks(db_session: AsyncSession) -> dict[str, list[InsPks]]:
    """Provide a database populated with composite key test data."""
    async with db_session.begin():
        men_data = [InsPks(id=i, name=f'man_{i}', sex='men') for i in range(1, 4)]
        women_data = [InsPks(id=i, name=f'woman_{i}', sex='women') for i in range(4, 7)]
        all_data = men_data + women_data

        db_session.add_all(all_data)
        await db_session.flush()

        return {'men': men_data, 'women': women_data, 'all': all_data}


@pytest_asyncio.fixture(scope='function')
async def setup_relationship_database() -> AsyncGenerator[None, None]:
    """Setup and teardown relationship database for each test function."""
    from tests.relationship_model import Base

    async with _async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with _async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture
async def relationship_session(setup_relationship_database) -> AsyncGenerator[AsyncSession, None]:
    """Provide a database session for relationship testing."""
    async with _async_session_factory() as session:
        yield session


@pytest.fixture
def user_crud() -> CRUDPlus[User]:
    """Provide CRUD instance for User model."""
    return CRUDPlus(User)


@pytest.fixture
def post_crud() -> CRUDPlus[Post]:
    """Provide CRUD instance for Post model."""
    return CRUDPlus(Post)


@pytest.fixture
def role_crud() -> CRUDPlus[Role]:
    """Provide CRUD instance for Role model."""
    return CRUDPlus(Role)


@pytest.fixture
def profile_crud() -> CRUDPlus[Profile]:
    """Provide CRUD instance for Profile model."""
    return CRUDPlus(Profile)


@pytest_asyncio.fixture
async def sample_data(relationship_session: AsyncSession) -> dict:
    """Create sample data for relationship testing."""
    from tests.relationship_model import Profile, user_role

    async with relationship_session.begin():
        # Create users
        users = []
        for i in range(1, 4):
            user_data = UserCreate(name=f'user_{i}')
            user = User(**user_data.model_dump())
            relationship_session.add(user)
            users.append(user)

        await relationship_session.flush()

        # Create user profiles
        profiles = []
        for i, user in enumerate(users[:2]):  # Only first 2 users get profiles
            profile = Profile(user_id=user.id, bio=f'Bio for {user.name}')
            relationship_session.add(profile)
            profiles.append(profile)

        # Create posts
        posts = []
        for i in range(5):
            post = Post(
                title=f'Post {i + 1}',
                author_id=users[i % len(users)].id,
            )
            relationship_session.add(post)
            posts.append(post)

        await relationship_session.flush()

        # Create roles
        roles = []
        for role_name in ['admin', 'editor', 'viewer']:
            role = Role(name=role_name)
            relationship_session.add(role)
            roles.append(role)

        await relationship_session.flush()

        # Create user-role relationships
        await relationship_session.execute(
            user_role.insert().values(
                [
                    {'user_id': users[0].id, 'role_id': roles[0].id},  # user_1 is admin
                    {'user_id': users[1].id, 'role_id': roles[1].id},  # user_2 is editor
                    {'user_id': users[0].id, 'role_id': roles[1].id},  # user_1 is also editor
                ]
            )
        )

        await relationship_session.commit()

    return {
        'users': users,
        'profiles': profiles,
        'posts': posts,
        'roles': roles,
    }
