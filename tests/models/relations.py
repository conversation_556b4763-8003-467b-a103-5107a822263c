#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from __future__ import annotations

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, Table
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship


class RelationBase(DeclarativeBase):
    pass


user_role = Table(
    'user_role',
    RelationBase.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('rel_user.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('rel_role.id'), primary_key=True),
)


class RelUser(RelationBase):
    __tablename__ = 'rel_user'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))

    profile: Mapped[RelProfile | None] = relationship(back_populates='user', uselist=False)
    posts: Mapped[list[RelPost]] = relationship(back_populates='author')
    roles: Mapped[list[RelRole]] = relationship(secondary=user_role, back_populates='users')


class RelProfile(RelationBase):
    __tablename__ = 'rel_profile'

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey('rel_user.id'), unique=True)
    bio: Mapped[str] = mapped_column(String(200))

    user: Mapped[RelUser] = relationship(back_populates='profile')


class RelCategory(RelationBase):
    __tablename__ = 'rel_category'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))
    parent_id: Mapped[int | None] = mapped_column(ForeignKey('rel_category.id'))

    parent: Mapped[RelCategory | None] = relationship('RelCategory', remote_side=[id], back_populates='children')
    children: Mapped[list[RelCategory]] = relationship('RelCategory', back_populates='parent')
    posts: Mapped[list[RelPost]] = relationship(back_populates='category')


class RelPost(RelationBase):
    __tablename__ = 'rel_post'

    id: Mapped[int] = mapped_column(primary_key=True)
    title: Mapped[str] = mapped_column(String(100))
    author_id: Mapped[int] = mapped_column(ForeignKey('rel_user.id'))
    category_id: Mapped[int | None] = mapped_column(ForeignKey('rel_category.id'))

    author: Mapped[RelUser] = relationship(back_populates='posts')
    category: Mapped[RelCategory | None] = relationship(back_populates='posts')


class RelRole(RelationBase):
    __tablename__ = 'rel_role'

    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(50))

    users: Mapped[list[RelUser]] = relationship(secondary=user_role, back_populates='roles')
