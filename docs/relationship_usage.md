# SQLAlchemy CRUD Plus - 关系查询使用指南

## 概述

SQLAlchemy CRUD Plus 通过 `options` 参数支持关系查询，使用标准的 SQLAlchemy 关系加载策略。

## 基本用法

### 关系加载

使用 `options` 参数加载关系数据：

```python
from sqlalchemy.orm import selectinload, joinedload

# 加载一对一关系
user = await user_crud.select_model(
    session, user_id,
    options=[selectinload(User.profile)]
)

# 加载一对多关系
user = await user_crud.select_model(
    session, user_id,
    options=[selectinload(User.posts)]
)

# 加载多对多关系
user = await user_crud.select_model(
    session, user_id,
    options=[selectinload(User.roles)]
)

# 加载多个关系
user = await user_crud.select_model(
    session, user_id,
    options=[
        selectinload(User.posts),
        joinedload(User.profile),
        selectinload(User.roles)
    ]
)
```

### 加载策略

#### selectinload (推荐用于一对多)
```python
users = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)]
)
```

#### joinedload (推荐用于一对一)
```python
users = await user_crud.select_models(
    session,
    options=[joinedload(User.profile)]
)
```

#### subqueryload (适合大量数据)
```python
from sqlalchemy.orm import subqueryload

users = await user_crud.select_models(
    session,
    options=[subqueryload(User.posts)]
)
```

## 支持的方法

所有查询方法都支持 `options` 参数：

### select_model
```python
user = await user_crud.select_model(
    session, user_id,
    options=[selectinload(User.posts)]
)
```

### select_model_by_column
```python
user = await user_crud.select_model_by_column(
    session,
    name='alice',
    options=[selectinload(User.posts)]
)
```

### select_models
```python
users = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)],
    limit=10
)
```

### select_models_order
```python
users = await user_crud.select_models_order(
    session,
    sort_columns='name',
    sort_orders='asc',
    options=[selectinload(User.posts)]
)
```

## 分页与关系

```python
# 分页查询并加载关系
page1 = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)],
    limit=10,
    offset=0
)

page2 = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)],
    limit=10,
    offset=10
)
```

## 性能优化

### 避免 N+1 查询
```python
# 错误：会产生 N+1 查询
users = await user_crud.select_models(session)
for user in users:
    print(len(user.posts))  # 每次访问都会触发查询

# 正确：预加载关系
users = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)]
)
for user in users:
    print(len(user.posts))  # 不会触发额外查询
```

### 选择合适的策略
- **一对一关系**: 使用 `joinedload`
- **一对多关系**: 使用 `selectinload`
- **大量数据**: 使用 `subqueryload`

## 模型定义

简洁的关系模型定义：

```python
from sqlalchemy import ForeignKey, Integer, String, Table
from sqlalchemy.orm import DeclarativeBase, Mapped, MappedAsDataclass, mapped_column, relationship

class Base(MappedAsDataclass, DeclarativeBase):
    pass

# 多对多关联表
user_role = Table(
    'user_role',
    Base.metadata,
    mapped_column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    mapped_column('role_id', Integer, ForeignKey('role.id'), primary_key=True),
)

@MappedAsDataclass
class User(Base):
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    name: Mapped[str] = mapped_column(String(50))

    # 关系定义
    profile: Mapped['Profile'] = relationship(back_populates='user', init=False)
    posts: Mapped[list['Post']] = relationship(back_populates='author', init=False)
    roles: Mapped[list['Role']] = relationship(secondary=user_role, back_populates='users', init=False)

@MappedAsDataclass
class Profile(Base):
    __tablename__ = 'profile'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    user_id: Mapped[int] = mapped_column(ForeignKey('user.id'))
    bio: Mapped[str] = mapped_column(String(200))

    user: Mapped['User'] = relationship(back_populates='profile', init=False)

@MappedAsDataclass
class Post(Base):
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    title: Mapped[str] = mapped_column(String(100))
    author_id: Mapped[int] = mapped_column(ForeignKey('user.id'))

    author: Mapped['User'] = relationship(back_populates='posts', init=False)

@MappedAsDataclass
class Role(Base):
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(primary_key=True, init=False)
    name: Mapped[str] = mapped_column(String(50))

    users: Mapped[list['User']] = relationship(secondary=user_role, back_populates='roles', init=False)
```

## 注意事项

1. **关系定义**: 使用 `init=False` 避免 MappedAsDataclass 初始化问题
2. **加载策略**: 根据数据量和查询模式选择合适的策略
3. **性能**: 始终预加载需要的关系数据以避免 N+1 查询
4. **简洁性**: 保持模型定义简洁，只包含必要的字段

## 总结

SQLAlchemy CRUD Plus 通过标准的 SQLAlchemy `options` 参数提供了简洁而强大的关系查询功能。通过合理使用加载策略，可以实现高效的关系数据查询，同时保持代码的简洁性和可维护性。
