# SQLAlchemy CRUD Plus - 关系查询文档

本文档详细介绍了如何使用 SQLAlchemy CRUD Plus 进行关系查询操作，支持 SQLAlchemy 2.0 和 Pydantic 2.0。

## 目录

1. [基本概念](#基本概念)
2. [关系过滤](#关系过滤)
3. [关系加载策略](#关系加载策略)
4. [JOIN 查询](#join-查询)
5. [复杂查询示例](#复杂查询示例)
6. [性能优化](#性能优化)
7. [错误处理](#错误处理)

## 基本概念

SQLAlchemy CRUD Plus 支持以下类型的关系：

- **一对一关系** (One-to-One): 如用户和用户档案
- **一对多关系** (One-to-Many): 如用户和文章
- **多对多关系** (Many-to-Many): 如用户和角色
- **自引用关系** (Self-Referential): 如分类的父子关系

## 关系过滤

### 基本语法

使用 `关系名__字段名` 的语法进行跨关系过滤：

```python
# 根据作者用户名过滤文章
posts = await post_crud.select_models(
    session,
    author__username='alice'
)

# 使用操作符
posts = await post_crud.select_models(
    session,
    author__username__like='user_%'
)
```

### 支持的操作符

- `eq`: 等于 (默认)
- `ne`: 不等于
- `gt`: 大于
- `ge`: 大于等于
- `lt`: 小于
- `le`: 小于等于
- `like`: 模糊匹配
- `ilike`: 不区分大小写的模糊匹配
- `in`: 在列表中
- `not_in`: 不在列表中
- `is_null`: 为空
- `is_not_null`: 不为空
- `between`: 在范围内
- `contains`: 包含
- `startswith`: 以...开始
- `endswith`: 以...结束

### 多重关系过滤

```python
# 同时根据作者和分类过滤
posts = await post_crud.select_models(
    session,
    author__username='alice',
    category__name='Technology'
)
```

## 关系加载策略

### 可用的加载策略

1. **selectinload** (推荐): 使用 SELECT IN 查询，适合一对多关系
2. **joinedload**: 使用 JOIN 查询，适合一对一关系
3. **subqueryload**: 使用子查询，适合大量数据
4. **contains_eager**: 与显式 JOIN 配合使用
5. **raiseload**: 禁止懒加载，访问时抛出异常
6. **noload**: 不加载关系数据

### 使用方法

```python
from sqlalchemy.orm import selectinload, joinedload

# 使用 selectinload 加载文章
user = await user_crud.select_model(
    session, 
    user_id,
    options=[selectinload(User.posts)]
)

# 使用 joinedload 加载档案
user = await user_crud.select_model(
    session,
    user_id,
    options=[joinedload(User.profile)]
)

# 加载多个关系
user = await user_crud.select_model(
    session,
    user_id,
    options=[
        selectinload(User.posts),
        joinedload(User.profile),
        selectinload(User.roles)
    ]
)
```

## JOIN 查询

### 基本 JOIN

```python
# 内连接
posts = await post_crud.select_models(
    session,
    joins=['author']
)

# 左连接
posts = await post_crud.select_models(
    session,
    joins=[{'target': 'category', 'type': 'left'}]
)
```

### JOIN 类型

- `inner`: 内连接 (默认)
- `left`: 左外连接
- `right`: 右外连接 (转换为左连接)
- `full`: 全外连接

### 多重 JOIN

```python
posts = await post_crud.select_models(
    session,
    joins=[
        'author',  # 内连接作者
        {'target': 'category', 'type': 'left'}  # 左连接分类
    ]
)
```

## 复杂查询示例

### 分页查询与关系加载

```python
from sqlalchemy.orm import selectinload

# 分页查询用户及其文章
users = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)],
    limit=10,
    offset=0
)
```

### 排序查询与关系过滤

```python
# 按用户名排序，只查询活跃用户的文章
posts = await post_crud.select_models_order(
    session,
    sort_columns='title',
    sort_orders='asc',
    author__is_active=True,
    options=[selectinload(Post.author)]
)
```

### 统计查询

```python
# 统计活跃用户的文章数量
count = await post_crud.count(
    session,
    author__is_active=True
)

# 检查是否存在特定分类的文章
exists = await post_crud.exists(
    session,
    category__name='Technology'
)
```

### 更新和删除操作

```python
# 根据关系条件更新
updated_count = await post_crud.update_model_by_column(
    session,
    {'is_published': True},
    allow_multiple=True,
    author__username='alice'
)

# 根据关系条件删除
deleted_count = await post_crud.delete_model_by_column(
    session,
    allow_multiple=True,
    author__is_active=False
)
```

## 性能优化

### 避免 N+1 查询问题

```python
# 错误：会产生 N+1 查询
users = await user_crud.select_models(session)
for user in users:
    print(len(user.posts))  # 每次访问都会触发查询

# 正确：预加载关系
users = await user_crud.select_models(
    session,
    options=[selectinload(User.posts)]
)
for user in users:
    print(len(user.posts))  # 不会触发额外查询
```

### 选择合适的加载策略

```python
# 一对一关系：使用 joinedload
user = await user_crud.select_model(
    session,
    user_id,
    options=[joinedload(User.profile)]
)

# 一对多关系：使用 selectinload
user = await user_crud.select_model(
    session,
    user_id,
    options=[selectinload(User.posts)]
)

# 大量数据：使用 subqueryload
users = await user_crud.select_models(
    session,
    options=[subqueryload(User.posts)]
)
```

### 批量操作

```python
# 批量加载多个关系
users = await user_crud.select_models(
    session,
    options=[
        selectinload(User.posts),
        joinedload(User.profile),
        selectinload(User.roles)
    ]
)
```

## 错误处理

### 常见错误类型

1. **RelationshipError**: 关系不存在或配置错误
2. **JoinError**: JOIN 操作失败
3. **ModelColumnError**: 模型字段不存在

### 错误处理示例

```python
from sqlalchemy_crud_plus.errors import RelationshipError, JoinError

try:
    posts = await post_crud.select_models(
        session,
        invalid_relationship__field='value'
    )
except RelationshipError as e:
    print(f"关系错误: {e}")

try:
    posts = await post_crud.select_models(
        session,
        joins=[{'target': 'invalid_relation', 'type': 'left'}]
    )
except JoinError as e:
    print(f"连接错误: {e}")
```

## 最佳实践

1. **明确加载策略**: 根据数据量和查询模式选择合适的加载策略
2. **避免过度加载**: 只加载需要的关系数据
3. **使用关系过滤**: 利用数据库的 JOIN 能力进行过滤
4. **合理使用分页**: 对大量数据进行分页处理
5. **错误处理**: 妥善处理关系查询中的异常情况

## 类型提示

所有方法都提供了完整的类型提示支持：

```python
from typing import Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.base import ExecutableOption

# 方法签名示例
async def select_models(
    self,
    session: AsyncSession,
    *whereclause: ColumnExpressionArgument[bool],
    options: list[ExecutableOption] | None = None,
    joins: list[JoinTarget] | None = None,
    limit: int | None = None,
    offset: int | None = None,
    **kwargs,
) -> Sequence[Model]:
    ...
```

这确保了在 IDE 中获得完整的代码补全和类型检查支持。
